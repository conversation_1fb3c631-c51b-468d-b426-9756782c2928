<?php

namespace Modules\Category\Entities;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Modules\Category\Database\factories\SubCategoryTranslateFactory;

class SubCategoryTranslate extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     */
    protected $fillable = [];

    protected static function newFactory(): SubCategoryTranslateFactory
    {
        //return SubCategoryTranslateFactory::new();
    }
}
