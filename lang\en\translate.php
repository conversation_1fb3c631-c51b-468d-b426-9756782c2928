<?php
 return array (
  'About Us' => 'About Us',
  'Home' => 'Home',
  'Best Feature' => 'Best Feature',
  'Our Latest Features' => 'Our Latest Features',
  'Contact Us' => 'Contact Us',
  'Get Services' => 'Get Services',
  'M' => 'M',
  'Total Freelancers' => 'Total Freelancers',
  'Total Services' => 'Total Services',
  'Total Job' => 'Total Job',
  'Testimonial' => 'Testimonial',
  'Received 4.8/5 Stars in Over 10,000+ Reviews.' => 'Received 4.8/5 Stars in Over 10,000+ Reviews.',
  'Very Solid!!' => 'Very Solid!!',
  'Our Faq' => 'Our Faq',
  'Explore Our Informative FAQ Section' => 'Explore Our Informative FAQ Section',
  'KYC' => 'KYC',
  'Sub Categories' => 'Sub Categories',
  'Sub Category' => 'Sub Category',
  'Document' => 'Document',
  'Document Name' => 'Document Name',
  'Subcategory' => 'Sub Category',
  'Select Subcategory' => 'Select SubCategory',
  'Commission' => 'Commission',
  'Subscription' => 'Subscription',
  'KYC Verifaction' => 'KYC Verifaction',
  'My Earnings' => 'My Earnings',
  'Manage KYC' => 'Manage KYC',
  'KYC Type' => 'KYC Type',
  'Pending List' => 'Pending List',
  'Approved List' => 'Approved List',
  'Rejected List' => 'Rejected List',
  'Active Orders' => 'Active Orders',
  'Manage Orders' => 'Manage Orders',
  'Commission Type' => 'Commission Type',
  'Commission per sale' => 'Commission per sale',
  'Sub Category List' => 'Sub Category List',
  'Serial' => 'Serial',
  'Service' => 'Service',
  'Buyer' => 'Buyer',
  'Seller' => 'Seller',
  'Amount' => 'Amount',
  'Status' => 'Status',
  'Action' => 'Action',
  'In-Progress' => 'In-Progress',
  'Complete' => 'Complete',
  'Cancel by Seller' => 'Cancel by Seller',
  'Cancel by Buyer' => 'Cancel by Buyer',
  'Rejected by Seller' => 'Rejected by Seller',
  'Awaiting for Approval' => 'Awaiting for Approval',
  'Show' => 'Show',
  'Delete' => 'Delete',
  'Delete Confirmation' => 'Delete Confirmation',
  'Are you realy want to delete this item?' => 'Are you realy want to delete this item?',
  'Close' => 'Close',
  'Yes, Delete' => 'Yes, Delete',
  'Login' => 'Login',
  'Login Here' => 'Login Here',
  'Email' => 'Email',
  'Password' => 'Password',
  'Login Now' => 'Login Now',
  'Welcome to WorkZone' => 'Welcome to ITQANY',
  'Awaiting Orders' => 'Awaiting Orders',
  'Cancel Orders' => 'Cancel Orders',
  'Completed Orders' => 'Completed Orders',
  'Dashboard' => 'Dashboard',
  'Active Order' => 'Active Order',
  'Complete Order' => 'Complete Order',
  'Cancel Order' => 'Cancel Order',
  'Rejected Order' => 'Rejected Order',
  'Order Statitics' => 'Order Statitics',
  'Latest Order' => 'Latest Order',
  'Purchase History' => 'Purchase History',
  'My Profile' => 'My Profile',
  'Edit Profile' => 'Edit Profile',
  'Basic Information' => 'Basic Information',
  'Click here to' => 'Click here to',
  'Choose File' => 'Choose File',
  'and upload' => 'and upload',
  'Name' => 'Name',
  'Designation' => 'Designation',
  'Facebook' => 'Facebook',
  'Linkedin' => 'Linkedin',
  'Twitter' => 'Tiktok',
  'Instagram' => 'Instagram',
  'About Me' => 'About Me',
  'Update' => 'Update',
  'Change Password' => 'Change Password',
  'Current Password' => 'Current Password',
  'New Password' => 'New Password',
  'Confirmed Password' => 'Confirmed Password',
  'Logout' => 'Logout',
  'All Orders' => 'All Orders',
  'Order Details' => 'Order Details',
  'Order Information' => 'Order Information',
  'Order ID' => 'Order ID',
  'Created At' => 'Created At',
  'Delivery Date' => 'Delivery Date',
  'Revision' => 'Revision',
  'Payment Information' => 'Payment Information',
  'Payment Status:' => 'Payment Status:',
  'Success' => 'Success',
  'Pending' => 'Pending',
  'Payment Gateway' => 'Payment Gateway',
  'Total' => 'Total',
  'Transaction' => 'Transaction',
  'Order Status' => 'Order Status',
  'Approved Now' => 'Approved Now',
  'Delete Order' => 'Delete Order',
  'Payment Approved' => 'Payment Approved',
  'Service & Package Information' => 'Service & Package Information',
  'Service & Package' => 'Service & Package',
  'Package' => 'Package',
  'Functional website' => 'Functional website',
  'Pages' => 'Pages',
  'Responsive design' => 'Responsive design',
  'Source file' => 'Source file',
  'Content upload' => 'Content upload',
  'Speed optimization' => 'Speed optimization',
  'Freelancer Info' => 'Freelancer Info',
  'Member Since' => 'Member Since',
  'Reviews' => 'Reviews',
  'Complete Confirmation' => 'Complete Confirmation',
  'Are you realy want to complete this item?' => 'Are you realy want to complete this item?',
  'Yes, Complete It' => 'Yes, Complete It',
  'Approval Confirmation' => 'Approval Confirmation',
  'Are you realy want to approved this item?' => 'Are you realy want to approved this item?',
  'Yes, Appoved It' => 'Yes, Appoved It',
  'Cancel Confirmation' => 'Cancel Confirmation',
  'Are you realy want to cancel this item?' => 'Are you realy want to cancel this item?',
  'Yes, Cancel It' => 'Yes, Cancel It',
  'Payment Confirmation' => 'Payment Confirmation',
  'Are you realy want to approved this payment?' => 'Are you realy want to approved this payment?',
  'Yes, Payment Approved' => 'Yes, Payment Approved',
  'Pending Payment Orders' => 'Pending Payment Orders',
  'Payment' => 'Payment',
  'success' => 'success',
  'pending' => 'pending',
  'Rejected Orders' => 'Rejected Orders',
  'Manage Seller' => 'Manage Seller',
  'Phone' => 'Phone',
  'Seller Details' => 'Seller Details',
  'Total Income' => 'Total Income',
  'Hired Job' => 'Hired Job',
  'Total Order' => 'Total Order',
  'Contact Information' => 'Contact Information',
  'Go To Public Profile' => 'Go To Public Profile',
  'Delete User' => 'Delete User',
  'Edit User Basic Information' => 'Edit User Basic Information',
  'Address' => 'Address',
  'Make Top Seller' => 'Make Top Seller',
  'Update Info' => 'Update Info',
  'Main Menu' => 'Main Menu',
  'Manage Order' => 'Manage Order',
  'Complete Orders' => 'Complete Orders',
  'Pending Payment Order' => 'Pending Payment Order',
  'Manage Service' => 'Manage Service',
  'Category List' => 'Category List',
  'City List' => 'City List',
  'Create Service' => 'Create Service',
  'All Services' => 'All Services',
  'Awaiting Service' => 'Awaiting Service',
  'Featured Service' => 'Featured Service',
  'Review List' => 'Review List',
  'Manage Job Post' => 'Manage Job Post',
  'All Job Post' => 'All Job Post',
  'Awaiting Job Post' => 'Awaiting Job Post',
  'Manage Withdraw' => 'Manage Withdraw',
  'Withdraw Method' => 'Withdraw Method',
  'Withdraw List' => 'Withdraw List',
  'Seller List' => 'Seller List',
  'Pending Seller' => 'Pending Seller',
  'Manage Buyer' => 'Manage Buyer',
  'Buyer List' => 'Buyer List',
  'Pending Buyer' => 'Pending Buyer',
  'Contact Message' => 'Contact Message',
  'CMS & Blogs' => 'CMS & Blogs',
  'Manage Blog' => 'Manage Blog',
  'Create Categroy' => 'Create Categroy',
  'Categroy List' => 'Categroy List',
  'Create Blog' => 'Create Blog',
  'Blog List' => 'Blog List',
  'Comment List' => 'Comment List',
  'Manage Pages' => 'Manage Pages',
  'Terms and Conditions' => 'Terms and Conditions',
  'Privacy Policy' => 'Privacy Policy',
  'FAQ' => 'FAQ',
  'Custom Page' => 'Custom Page',
  'Manage Section' => 'Manage Section',
  'Intro Section' => 'Intro Section',
  'Home-2' => 'Home-2',
  'Working Step' => 'Working Step',
  'Our Feature' => 'Our Feature',
  'Join Seller' => 'Join Seller',
  'Explore Us' => 'Explore Us',
  'Footer Info' => 'Footer Info',
  'Setting & Configuration' => 'Setting & Configuration',
  'Setting' => 'Setting',
  'Multi Currency' => 'Multi Currency',
  'Language' => 'Language',
  'Languages' => 'Languages',
  'Theme Languages' => 'Theme Languages',
  'Email Configuration' => 'Email Configuration',
  'Configuration' => 'Configuration',
  'Email Template' => 'Email Template',
  'Website Setup' => 'Website Setup',
  'Cookie Consent' => 'Cookie Consent',
  'Error Page' => 'Error Page',
  'Login Page' => 'Login Page',
  'Admin Login' => 'Admin Login',
  'Breadcrumb Image' => 'Breadcrumb Image',
  'Social Login' => 'Social Login',
  'Default Avatar' => 'Default Avatar',
  'Maintenance mode' => 'Maintenance mode',
  'SEO Setup' => 'SEO Setup',
  'Payment Method' => 'Payment Method',
  'Others' => 'Others',
  'Newsletter' => 'Newsletter',
  'Subscriber List' => 'Subscriber List',
  'Send Mail' => 'Send Mail',
  'Cache Clear' => 'Cache Clear',
  'Version' => 'Version',
  'Buyer Details' => 'Buyer Details',
  'Delete Seller' => 'Delete Seller',
  'WorkZone || Forget Password' => 'ITQANY || Forget Password',
  'Forget Password' => 'Forget Password',
  'Welcome to Work Zone' => 'Welcome to ITQANY',
  'Send Resend Link' => 'Send Resend Link',
  'OR' => 'OR',
  'Do not have an account ?' => 'Do not have an account ?',
  'Create Account' => 'Create Account',
  'WorkZone || Sign In' => 'ITQANY || Sign In',
  'Sign In' => 'Sign In',
  'Remember Me' => 'Remember Me',
  'Forget Password?' => 'Forget Password?',
  'Buyer || Sign Up' => 'Buyer || Sign Up',
  'Sign Up' => 'Sign Up',
  'Confirm Password' => 'Confirm Password',
  'Already have an account?' => 'Already have an account?',
  'WorkZone || Reset Password' => 'ITQANY || Reset Password',
  'Reset Password' => 'Reset Password',
  'Reset Now' => 'Reset Now',
  'Seller || Sign Up' => 'Seller || Sign Up',
  'Blog Details' => 'Blog Details',
  'Comment' => 'Comment',
  'Tag' => 'Tag',
  'Share' => 'Share',
  'Comments' => 'Comments',
  'Write a Comment' => 'Write a Comment',
  'Submit Now' => 'Submit Now',
  'Our Blogs' => 'Our Blogs',
  'Learn More' => 'Learn More',
  'Sorry!! Blog Not Found' => 'Sorry!! Blog Not Found',
  'Whoops... this information is not available for a moment' => 'Whoops... this information is not available for a moment',
  'Back To Blogs' => 'Back To Blogs',
  'Buyer || Account Delete' => 'Buyer || Account Delete',
  'Account Delete' => 'Account Delete',
  'Notice: Remember you will not able to login this account' => 'Notice: Remember you will not able to login this account',
  'after delete your account.' => 'after delete your account.',
  'Yes Delete' => 'Yes Delete',
  'Cancel' => 'Cancel',
  'Are you realy want to delete your account ?' => 'Are you realy want to delete your account ?',
  'Yes, Delete It' => 'Yes, Delete It',
  'Buyer || Change Password' => 'Buyer || Change Password',
  'Update your Password' => 'Update your Password',
  'Do you want to change your password, please fill out the form below' => 'Do you want to change your password, please fill out the form below',
  'Update Password' => 'Update Password',
  'Buyer || Dashboard' => 'Buyer || Dashboard',
  'Post a Job' => 'Post a Job',
  'Freelancer' => 'Freelancer',
  'Buyer || Edit Profile' => 'Buyer || Edit Profile',
  'Gender' => 'Gender',
  'Select' => 'Select',
  'Male' => 'Male',
  'Female' => 'Female',
  'Upload Profile Image' => 'Upload Profile Image',
  'Profile Image' => 'Profile Image',
  'Save Now' => 'Save Now',
  'My Job' => 'My Job',
  'My Orders' => 'My Orders',
  'Wishlist' => 'Wishlist',
  'My Order' => 'My Order',
  'Post a job' => 'Post a job',
  'My jobs' => 'My jobs',
  'Are you sure you want to Logout' => 'Are you sure you want to Logout',
  'Workzone' => 'ITQANY',
  'Yes Logout' => 'Yes Logout',
  'Buyer || Order Details' => 'Buyer || Order Details',
  'Write Review' => 'Write Review',
  'Write Your Feedback' => 'Write Your Feedback',
  'Rating' => 'Rating',
  'Review' => 'Review',
  'Submit Review' => 'Submit Review',
  'Are you really want to complete the order?' => 'Are you really want to complete the order?',
  'Are you really want to cancel the order?' => 'Are you really want to cancel the order?',
  'Buyer || My Orders' => 'Buyer || My Orders',
  'All' => 'All',
  'Active' => 'Active',
  'Awaiting' => 'Awaiting',
  'Rejected' => 'Rejected',
  'Order Empty' => 'Order Empty',
  'You do not have any active order' => 'You do not have any active order',
  'Thank you' => 'Thank you',
  'You do not have any order' => 'You do not have any order',
  'You do not have any awaiting order' => 'You do not have any awaiting order',
  'You do not have any rejected order' => 'You do not have any rejected order',
  'You do not have any cancel order' => 'You do not have any cancel order',
  'You do not have any complete order' => 'You do not have any complete order',
  'Profile' => 'Profile',
  'Buyer Profile' => 'Buyer Profile',
  'Jobs' => 'Jobs',
  'Hired' => 'Hired',
  'Location' => 'Location',
  'About' => 'About',
  'Job Posts' => 'Job Posts',
  'Hourly' => 'Hourly',
  'Daily' => 'Daily',
  'Monthly' => 'Monthly',
  'Yearly' => 'Yearly',
  'Urgent' => 'Urgent',
  'From' => 'From',
  'Apply Now' => 'Apply Now',
  'Follow Us' => 'Follow Us',
  'Subject' => 'Subject',
  'Message' => 'Message',
  'Send Message' => 'Send Message',
  'Prev' => 'Prev',
  'Next' => 'Next',
  404 => '404',
  'Oops! Page not found' => 'Oops! Page not found',
  'Back to Home Page' => 'Back to Home Page',
  'Seller Detail' => 'Seller Detail',
  'Top Seller' => 'Top Seller',
  'hr' => 'hr',
  'Rate' => 'Rate',
  'Skills' => 'Skills',
  'Education' => 'Education',
  'Star' => 'Star',
  'Our Freelancers' => 'Our Freelancers',
  'Search..' => 'Search..',
  'All Categories' => 'All Categories',
  'Default' => 'Default',
  'A to Z' => 'A to Z',
  'ASC' => 'ASC',
  'Z to A' => 'Z to A',
  'DSC' => 'DSC',
  'Price' => 'Price',
  'Low to High' => 'Low to High',
  'High to Low' => 'High to Low',
  'Sort By' => 'Sort By',
  'Most Relevant' => 'Most Relevant',
  'View Profile' => 'View Profile',
  'Sorry!! Freelancer Not Found' => 'Sorry!! Freelancer Not Found',
  'Search for any service...' => 'Search for any service...',
  'Search' => 'Search',
  'Happy Customers' => 'Happy Customers',
  'Our Popular Categories' => 'Our Popular Categories',
  'Get some Inspirations from 86K+ skills' => 'Get some Inspirations from 86K+ skills',
  'Services' => 'Services',
  'Featured Services' => 'Featured Services',
  'Get best services for your work' => 'Get best services for your work',
  'Recent Job Post' => 'Recent Job Post',
  'Let start your career with us' => 'Let start your career with us',
  'Find our best service provider' => 'Find our best service provider',
  'View More' => 'View More',
  'Get Started' => 'Get Started',
  'Recently Added' => 'Recently Added',
  'Popular Search' => 'Popular Search',
  'Job Details' => 'Job Details',
  'Job type' => 'Job type',
  'Category' => 'Category',
  'Submit Proposal' => 'Submit Proposal',
  'Write your proposal' => 'Write your proposal',
  'Job Title or Keywords' => 'Job Title or Keywords',
  'All Cities' => 'All Cities',
  'Find Job' => 'Find Job',
  'Sorry!! Jobpost Not Found' => 'Sorry!! Jobpost Not Found',
  'Home One' => 'Home One',
  'Home Two' => 'Home Two',
  'Job Post' => 'Job Post',
  'Freelancers' => 'Freelancers',
  'Terms & Conditions' => 'Terms & Conditions',
  'Contact' => 'Contact',
  'Cookies' => 'Cookies',
  'Accept' => 'Accept',
  'Subscribe to Our Newsletter' => 'Subscribe to Our Newsletter',
  'We will keep you updated with the best new jobs.' => 'We will keep you updated with the best new jobs.',
  'Enter your email address' => 'Enter your email address',
  'Subscribe' => 'Subscribe',
  'Download App' => 'Download App',
  'Service Categories' => 'Service Categories',
  'Blog Categories' => 'Blog Categories',
  'Important Links' => 'Important Links',
  'My Dashboard' => 'My Dashboard',
  'All Language keywords are not implemented in the demo mode' => 'All Language keywords are not implemented in the demo mode',
  'Admin can translate every word from the admin panel' => 'Admin can translate every word from the admin panel',
  'Please login first' => 'Please login first',
  'Please Login First' => 'Please Login First',
  'Maintenance' => 'Maintenance',
  'Package Information' => 'Package Information',
  'Basic' => 'Basic',
  'Day Delivery' => 'Day Delivery',
  'Revisions' => 'Revisions',
  'Standard' => 'Standard',
  'Pay' => 'Pay',
  'Pay via Stripe' => 'Pay via Stripe',
  'Card Number' => 'Card Number',
  'Expired Month' => 'Expired Month',
  'Expired Year' => 'Expired Year',
  'CVC' => 'CVC',
  'Please provide your valid card information' => 'Please provide your valid card information',
  'Pay Now' => 'Pay Now',
  'Pay via Bank' => 'Pay via Bank',
  'Transaction information' => 'Transaction information',
  'Something went wrong, please try again' => 'Something went wrong, please try again',
  'Seller || Account Delete' => 'Seller || Account Delete',
  'Seller || Change Password' => 'Seller || Change Password',
  'Seller || Dashboard' => 'Seller || Dashboard',
  'Seller || Edit Profile' => 'Seller || Edit Profile',
  'Hourly Rate' => 'Hourly Rate',
  'University Information' => 'University Information',
  'Institute' => 'Institute',
  'Time Period' => 'Time Period',
  'High School Information' => 'High School Information',
  'Your Skills' => 'Your Skills',
  'Job Applications' => 'Job Applications',
  'My Withdraw' => 'My Withdraw',
  'Seller || Order Details' => 'Seller || Order Details',
  'Buyer Info' => 'Buyer Info',
  'Are you really want to approved the order?' => 'Are you really want to approved the order?',
  'Yes, Approved It' => 'Yes, Approved It',
  'Are you really want to rejected the order?' => 'Are you really want to rejected the order?',
  'Yes, Rejected It' => 'Yes, Rejected It',
  'Seller || My Orders' => 'Seller || My Orders',
  'Service Detail' => 'Service Detail',
  'About this gig' => 'About this gig',
  'Premium' => 'Premium',
  'Order Now' => 'Order Now',
  'Our Services' => 'Our Services',
  'Featured' => 'Featured',
  'Sorry!! Service Not Found' => 'Sorry!! Service Not Found',
  'Email is required' => 'Email is required',
  'Password is required' => 'Password is required',
  'Login successfully' => 'Login successfully',
  'Credential does not match' => 'Credential does not match',
  'Inactive your account' => 'Inactive your account',
  'Email not found' => 'Email not found',
  'Logout successfully' => 'Logout successfully',
  'Order cancel successful' => 'Order cancel successful',
  'Order approval successful' => 'Order approval successful',
  'Order complete successful' => 'Order complete successful',
  'Payment approval successful' => 'Payment approval successful',
  'Order delete successful' => 'Order delete successful',
  'Update successfully' => 'Update successfully',
  'Password changed successfully' => 'Password changed successfully',
  'Current password does not match' => 'Current password does not match',
  'Name is required' => 'Name is required',
  'Phone is required' => 'Phone is required',
  'Address is required' => 'Address is required',
  'User updated successful' => 'User updated successful',
  'You can not delete this user, multiple listing or jobpost available under this user' => 'You can not delete this user, multiple listing or jobpost available under this user',
  'Delete Successfully' => 'Delete Successfully',
  'Status Changed Successfully' => 'Status Changed Successfully',
  'Please try to login with social media' => 'Please try to login with social media',
  'Please verify your email' => 'Please verify your email',
  'A password reset link has been send to your mail' => 'A password reset link has been send to your mail',
  'Invalid token, please try again' => 'Invalid token, please try again',
  'Email already exist' => 'Email already exist',
  'Confirm password does not match' => 'Confirm password does not match',
  'You have to provide minimum 4 character password' => 'You have to provide minimum 4 character password',
  'Password reset successfully' => 'Password reset successfully',
  'Login Successfully' => 'Login Successfully',
  'Account created successful, a verification link has been send to your mail, please verify it' => 'Account created successful, a verification link has been send to your mail, please verify it',
  'Email already verified' => 'Email already verified',
  'Verification Successfully' => 'Verification Successfully',
  'Invalid token or email' => 'Invalid token or email',
  'Updated successfully' => 'Updated successfully',
  'Rating is required' => 'Rating is required',
  'Review is required' => 'Review is required',
  'Review already submited' => 'Review already submited',
  'Review submited successful, please wait for admin approval' => 'Review submited successful, please wait for admin approval',
  'Your account deleted successful' => 'Your account deleted successful',
  'Comment is required' => 'Comment is required',
  'Comment submited successfully' => 'Comment submited successfully',
  'Language switched successful' => 'Language switched successful',
  'Currency switched successful' => 'Currency switched successful',
  'Something went wrong' => 'Something went wrong',
  'Your payment has been made successful. Thanks for your new purchase' => 'Your payment has been made successful. Thanks for your new purchase',
  'Transaction field is required' => 'Transaction field is required',
  'Your payment has been made. please wait for admin payment approval' => 'Your payment has been made. please wait for admin payment approval',
  'This Is Demo Version. You Can Not Change Anything' => 'This Is Demo Version. You Can Not Change Anything',
  'Please provide valid mollie api key' => 'Please provide valid mollie api key',
  'Order rejected successful' => 'Order rejected successful',
  'Hourly rate should be numeric' => 'Hourly rate should be numeric',
  'Current password is required' => 'Current password is required',
  'Password confirmation is required' => 'Password confirmation is required',
  'Created successfully' => 'Created successfully',
  'You can not delete it, multiple blog available on this category' => 'You can not delete it, multiple blog available on this category',
  'Deleted successfully' => 'Deleted successfully',
  'Name already exist' => 'Name already exist',
  'Slug is required' => 'Slug is required',
  'Slug already exist' => 'Slug already exist',
  'Title is required' => 'Title is required',
  'Title already exist' => 'Title already exist',
  'Image is required' => 'Image is required',
  'Description is required' => 'Description is required',
  'Category is required' => 'Category is required',
  'Blog Category' => 'Blog Category',
  'Create New' => 'Create New',
  'Total Blog' => 'Total Blog',
  'Inactive' => 'Inactive',
  'Edit' => 'Edit',
  'Create Category' => 'Create Category',
  'Slug' => 'Slug',
  'Visibility Status' => 'Visibility Status',
  'Edit Category' => 'Edit Category',
  'Switch to language translation' => 'Switch to language translation',
  'Your editing mode' => 'Your editing mode',
  'Blog' => 'Blog',
  'Click Here' => 'Click Here',
  'Comment Detail' => 'Comment Detail',
  'Created' => 'Created',
  'Go to Blog' => 'Go to Blog',
  'Change Status' => 'Change Status',
  'Image' => 'Image',
  'Title' => 'Title',
  'Select Category' => 'Select Category',
  'Description' => 'Description',
  'Tags' => 'Tags',
  'SEO Title' => 'SEO Title',
  'SEO Description' => 'SEO Description',
  'Edit Blog' => 'Edit Blog',
  'Created Successfully' => 'Created Successfully',
  'Update Successfully' => 'Update Successfully',
  'Multiple listing and jobpost created under it, so you can not delete it' => 'Multiple listing and jobpost created under it, so you can not delete it',
  'Save' => 'Save',
  'Multiple jobpostt created under it, so you can not delete it' => 'Multiple jobpostt created under it, so you can not delete it',
  'Create City' => 'Create City',
  'City Name' => 'City Name',
  'Edit City' => 'Edit City',
  'Your message has send successfully' => 'Your message has send successfully',
  'Subject is required' => 'Subject is required',
  'Message is required' => 'Message is required',
  'You can not delete this currency, this currency connected to multiple gateway' => 'You can not delete this currency, this currency connected to multiple gateway',
  'You can not delete USD currency' => 'You can not delete USD currency',
  'Currency name is required' => 'Currency name is required',
  'Currency name already exist' => 'Currency name already exist',
  'Country code is required' => 'Country code is required',
  'Country code already exist' => 'Country code already exist',
  'Currency code is required' => 'Currency code is required',
  'Currency code already exist' => 'Currency code already exist',
  'Currency icon is required' => 'Currency icon is required',
  'Currency icon already exist' => 'Currency icon already exist',
  'Currency rate is required' => 'Currency rate is required',
  'Currency rate must be number' => 'Currency rate must be number',
  'Currency position is required' => 'Currency position is required',
  'Create currency' => 'Create currency',
  'Manage Currency' => 'Manage Currency',
  'Currency List' => 'Currency List',
  'Currency Name' => 'Currency Name',
  'Currency Code' => 'Currency Code',
  'Country Code' => 'Country Code',
  'Currency Icon' => 'Currency Icon',
  'Currency Rate(per USD)' => 'Currency Rate(per USD)',
  'Currency Position' => 'Currency Position',
  'Before Price' => 'Before Price',
  'Before Price With Space' => 'Before Price With Space',
  'After Price' => 'After Price',
  'After Price With Space' => 'After Price With Space',
  'Make a default' => 'Make a default',
  'Edit currency' => 'Edit currency',
  'Currency' => 'Currency',
  'Code' => 'Code',
  'Country' => 'Country',
  'Icon' => 'Icon',
  'No' => 'No',
  'Sender name is required' => 'Sender name is required',
  'Mail host is required' => 'Mail host is required',
  'Smtp username is required' => 'Smtp username is required',
  'Smtp password is required' => 'Smtp password is required',
  'Mail port is required' => 'Mail port is required',
  'Mail encryption is required' => 'Mail encryption is required',
  'Edit Email Template' => 'Edit Email Template',
  'Dynamic Keyword' => 'Dynamic Keyword',
  'Keyword' => 'Keyword',
  'Meaning' => 'Meaning',
  'User Name' => 'User Name',
  'User Email' => 'User Email',
  'User Phone' => 'User Phone',
  'Message Subject' => 'Message Subject',
  'Message Details' => 'Message Details',
  'Sender Name' => 'Sender Name',
  'Mail Host' => 'Mail Host',
  'SMTP User Name' => 'SMTP User Name',
  'SMTP Password' => 'SMTP Password',
  'Mail Port' => 'Mail Port',
  'TLS' => 'TLS',
  'SSL' => 'SSL',
  'Verification Link' => 'Verification Link',
  'Reset Link' => 'Reset Link',
  'Email Template List' => 'Email Template List',
  'Template Name' => 'Template Name',
  'Question is required' => 'Question is required',
  'Answer is required' => 'Answer is required',
  'Create FAQ' => 'Create FAQ',
  'FAQ List' => 'FAQ List',
  'Question' => 'Question',
  'Answer' => 'Answer',
  'Edit FAQ' => 'Edit FAQ',
  'Database clear successfully' => 'Database clear successfully',
  'App id is required' => 'App id is required',
  'App name is required' => 'App name is required',
  'Analytic id is required' => 'Analytic id is required',
  'Site key is required' => 'Site key is required',
  'Secret key is required' => 'Secret key is required',
  'Facebook app id is required' => 'Facebook app id is required',
  'Facebook app secret is required' => 'Facebook app secret is required',
  'Facebook redirect url is required' => 'Facebook redirect url is required',
  'Gmail client id is required' => 'Gmail client id is required',
  'Gmail secret id is required' => 'Gmail secret id is required',
  'Gmail redirect url is required' => 'Gmail redirect url is required',
  'Chat link is required' => 'Chat link is required',
  'Admin Login Page' => 'Admin Login Page',
  'About us' => 'About us',
  'Play store link' => 'Play store link',
  'App store link' => 'App store link',
  'Copyright' => 'Copyright',
  'Social Media' => 'Social Media',
  'General Setting' => 'General Setting',
  'Logo and Favicon' => 'Logo and Favicon',
  'Google reCaptcha' => 'Google reCaptcha',
  'Tawk Chat' => 'Tawk Chat',
  'Google Analytic' => 'Google Analytic',
  'Facebook Pixel' => 'Facebook Pixel',
  'Database Clear' => 'Database Clear',
  'App Name' => 'App Name',
  'Select Theme' => 'Select Theme',
  'All Theme' => 'All Theme',
  'Theme One' => 'Theme One',
  'Theme Two' => 'Theme Two',
  'Contact Message Receiver Email' => 'Contact Message Receiver Email',
  'Timezone' => 'Timezone',
  'Website Logo' => 'Website Logo',
  'Footer Logo' => 'Footer Logo',
  'Website favicon' => 'Website favicon',
  'Captcha Site Key' => 'Captcha Site Key',
  'Captcha Secret Key' => 'Captcha Secret Key',
  'Tawk Chat Link' => 'Tawk Chat Link',
  'Analytic Id' => 'Analytic Id',
  'Pixel App Id' => 'Pixel App Id',
  'Warning' => 'Warning',
  'If you want to use the software from scratch, you can click here to reset the database. You do not need to remove the existing data one by one.' => 'If you want to use the software from scratch, you can click here to reset the database. You do not need to remove the existing data one by one.',
  'Clear Now' => 'Clear Now',
  'Clear Confirmation' => 'Clear Confirmation',
  'Are you realy want to clear this database?' => 'Are you realy want to clear this database?',
  'Yes, Clear Now' => 'Yes, Clear Now',
  'Maintenance Status' => 'Maintenance Status',
  'Announcement Text' => 'Announcement Text',
  'Facebook Login' => 'Facebook Login',
  'Facebook Client Id' => 'Facebook Client Id',
  'Facebook App Secret' => 'Facebook App Secret',
  'Redirect URL' => 'Redirect URL',
  'Google Login' => 'Google Login',
  'Gmail Client Id' => 'Gmail Client Id',
  'Gmail Secret Id' => 'Gmail Secret Id',
  'Updated Successfully' => 'Updated Successfully',
  'Apporval Successfully' => 'Apporval Successfully',
  'Job assigned successfully' => 'Job assigned successfully',
  'Job already has assigned, so you can not assign again' => 'Job already has assigned, so you can not assign again',
  'To apply the job, you have to logged in as a seller' => 'To apply the job, you have to logged in as a seller',
  'Application already submited' => 'Application already submited',
  'Job already has assigned, so you can not apply' => 'Job already has assigned, so you can not apply',
  'Your application has submited successfully, please wait for agent approval' => 'Your application has submited successfully, please wait for agent approval',
  'User is required' => 'User is required',
  'City is required' => 'City is required',
  'Price is required' => 'Price is required',
  'Price should be numeric' => 'Price should be numeric',
  'User' => 'User',
  'Visibility' => 'Visibility',
  'Job Status' => 'Job Status',
  'Approved' => 'Approved',
  'Buyer || Post a Job' => 'Buyer || Post a Job',
  'Job Info' => 'Job Info',
  'Job Title' => 'Job Title',
  'City' => 'City',
  'Select City' => 'Select City',
  'Start from' => 'Start from',
  'Job Type' => 'Job Type',
  'Upload Image' => 'Upload Image',
  'Thumbnail Image' => 'Thumbnail Image',
  'Buyer || Edit Job' => 'Buyer || Edit Job',
  'Edit Job' => 'Edit Job',
  'Choose New File' => 'Choose New File',
  'Buyer || Job Post' => 'Buyer || Job Post',
  'My Jobs' => 'My Jobs',
  'Proposal' => 'Proposal',
  'Date' => 'Date',
  'Job Post Empty' => 'Job Post Empty',
  'You do not have any job post' => 'You do not have any job post',
  'You do not have any job post under the admin feedback' => 'You do not have any job post under the admin feedback',
  'You do not have any active job post.' => 'You do not have any active job post.',
  'You do not have any hired job post.' => 'You do not have any hired job post.',
  'Are you realy want to delete this item ?' => 'Are you realy want to delete this item ?',
  'Buyer || Job Applications' => 'Buyer || Job Applications',
  'SN' => 'SN',
  'Actions' => 'Actions',
  'See more' => 'See more',
  'Approve' => 'Approve',
  'Application Details' => 'Application Details',
  'Apply Date' => 'Apply Date',
  'Are you realy want to approve this item?' => 'Are you realy want to approve this item?',
  'Yes, Approved' => 'Yes, Approved',
  'Create Job Post' => 'Create Job Post',
  'User/Buyer' => 'User/Buyer',
  'Select User' => 'Select User',
  'Start Price' => 'Start Price',
  'Save Data' => 'Save Data',
  'Edit Job Post' => 'Edit Job Post',
  'Update Data' => 'Update Data',
  'Make Approval' => 'Make Approval',
  'Applications' => 'Applications',
  'Click here' => 'Click here',
  'Created at' => 'Created at',
  'Resume' => 'Resume',
  'Download' => 'Download',
  'Seller || My Job Applications' => 'Seller || My Job Applications',
  'My Job Applications' => 'My Job Applications',
  'Job Application Empty' => 'Job Application Empty',
  'You do not have any job application' => 'You do not have any job application',
  'You do not have any hired job application' => 'You do not have any hired job application',
  'You do not have any pending job application' => 'You do not have any pending job application',
  'You do not have any rejected job application' => 'You do not have any rejected job application',
  'You can not delete english language' => 'You can not delete english language',
  'Requested language does not exist' => 'Requested language does not exist',
  'Code is required' => 'Code is required',
  'Code already exist' => 'Code already exist',
  'Create Language' => 'Create Language',
  'Language List' => 'Language List',
  'Language Name' => 'Language Name',
  'Language Code' => 'Language Code',
  'Direction' => 'Direction',
  'Left to Right' => 'Left to Right',
  'Right to left' => 'Right to left',
  'Edit Language' => 'Edit Language',
  'Right to Left' => 'Right to Left',
  'Enable' => 'Enable',
  'Disable' => 'Disable',
  'Theme language' => 'Theme language',
  'Multiple order created under it, so you can not delete it' => 'Multiple order created under it, so you can not delete it',
  'Images uploaded successfully' => 'Images uploaded successfully',
  'Images uploaded Failed' => 'Images uploaded Failed',
  'Featured Successfully' => 'Featured Successfully',
  'Featured removed Successfully' => 'Featured removed Successfully',
  'Deleted Successfully' => 'Deleted Successfully',
  'Review approval successfully' => 'Review approval successfully',
  'Created successfully, please awaiting for admin approval' => 'Created successfully, please awaiting for admin approval',
  'Seller is required' => 'Seller is required',
  'Package description is required' => 'Package description is required',
  'Delivery date is required' => 'Delivery date is required',
  'Revision is required' => 'Revision is required',
  'Functional website is required' => 'Functional website is required',
  'Page is required' => 'Page is required',
  'Responsive is required' => 'Responsive is required',
  'Source code is required' => 'Source code is required',
  'Content upload is required' => 'Content upload is required',
  'Speed optimized is required' => 'Speed optimized is required',
  'Awaiting for approval' => 'Awaiting for approval',
  'Gallery' => 'Gallery',
  'Select Seller' => 'Select Seller',
  'Pricing Package' => 'Pricing Package',
  'Package Name' => 'Package Name',
  'Description Here' => 'Description Here',
  'Delivery Time' => 'Delivery Time',
  'Days' => 'Days',
  'Functional Website' => 'Functional Website',
  'Yes' => 'Yes',
  'Number of Page' => 'Number of Page',
  'Responsive' => 'Responsive',
  'Source Code' => 'Source Code',
  'Content Upload' => 'Content Upload',
  'Speed Optimized' => 'Speed Optimized',
  'SEO Information' => 'SEO Information',
  'SEO title' => 'SEO title',
  'Edit Service' => 'Edit Service',
  'Make Featured' => 'Make Featured',
  'Remove Featured' => 'Remove Featured',
  'Featured Confirmation' => 'Featured Confirmation',
  'Are you realy want to featured this item?' => 'Are you realy want to featured this item?',
  'Yes, Featured' => 'Yes, Featured',
  'Are you realy want to removed featured this item?' => 'Are you realy want to removed featured this item?',
  'Yes, Removed' => 'Yes, Removed',
  'Service Gallery' => 'Service Gallery',
  'Upload Images' => 'Upload Images',
  'Gallery Images' => 'Gallery Images',
  'All Service' => 'All Service',
  'Review Detail' => 'Review Detail',
  'Listing' => 'Listing',
  'Crated at' => 'Crated at',
  'star' => 'star',
  'Visible' => 'Visible',
  'Details' => 'Details',
  'Seller || Create Service' => 'Seller || Create Service',
  'Basic Info' => 'Basic Info',
  'Select Delivery Time' => 'Select Delivery Time',
  'Price Here' => 'Price Here',
  'Upload Thumbnail' => 'Upload Thumbnail',
  'Publish Now' => 'Publish Now',
  'Seller || Edit Service' => 'Seller || Edit Service',
  'Update Now' => 'Update Now',
  'Gallery Image' => 'Gallery Image',
  'Seller || Manage Service' => 'Seller || Manage Service',
  'Service Empty' => 'Service Empty',
  'You do not have any service' => 'You do not have any service',
  'You do not have any active service' => 'You do not have any active service',
  'You do not have any pending service' => 'You do not have any pending service',
  'Mail send successfully' => 'Mail send successfully',
  'Something Went Wrong' => 'Something Went Wrong',
  'A verification link send to your email' => 'A verification link send to your email',
  'Newsletter verification successful' => 'Newsletter verification successful',
  'Send Mail to Subscriber' => 'Send Mail to Subscriber',
  'Facebook is required' => 'Facebook is required',
  'Twitter is required' => 'Tiktok is required',
  'Linkedin is required' => 'Linkedin is required',
  'Instagram is required' => 'Instagram is required',
  'Copyright is required' => 'Copyright is required',
  'Playstore is required' => 'Playstore is required',
  'Appstore is required' => 'Appstore is required',
  'About us is required' => 'About us is required',
  'Option is required' => 'Option is required',
  'Google Map is required' => 'Google Map is required',
  'Contact description is required' => 'Contact description is required',
  'Page name is required' => 'Page name is required',
  'Page name already exist' => 'Page name already exist',
  'Customer is required' => 'Customer is required',
  'Service is required' => 'Service is required',
  'Job is required' => 'Job is required',
  'Privacy policy is required' => 'Privacy policy is required',
  'Terms and condition is required' => 'Terms and condition is required',
  'About Us Image' => 'About Us Image',
  'Short Title' => 'Short Title',
  'Option one' => 'Option one',
  'Option two' => 'Option two',
  'Option three' => 'Option three',
  'CEO image' => 'CEO image',
  'Signature' => 'Signature',
  'Optional' => 'Optional',
  'Google Embed Map Link' => 'Google Embed Map Link',
  'Create Custom Page' => 'Create Custom Page',
  'Custom Page List' => 'Custom Page List',
  'Page Name' => 'Page Name',
  'Edit Custom Page' => 'Edit Custom Page',
  'Explore Image' => 'Explore Image',
  'Total Customer' => 'Total Customer',
  'Total Service' => 'Total Service',
  'Footer Information' => 'Footer Information',
  'Total Rating' => 'Total Rating',
  'For colorfull title, write the title inside <span>colorfull title here</span> tag' => 'For colorfull title, write the title inside <span>colorfull title here</span> tag',
  'Background Image' => 'Background Image',
  'Foreground Image' => 'Foreground Image',
  'Existing Image' => 'Existing Image',
  'Feature one image' => 'Feature one image',
  'Feature one title' => 'Feature one title',
  'Feature two image' => 'Feature two image',
  'Feature two title' => 'Feature two title',
  'Feature three image' => 'Feature three image',
  'Feature three title' => 'Feature three title',
  'Feature four image' => 'Feature four image',
  'Feature four title' => 'Feature four title',
  'Feature five image' => 'Feature five image',
  'Feature five title' => 'Feature five title',
  'Icon one' => 'Icon one',
  'Title one' => 'Title one',
  'Description one' => 'Description one',
  'Icon two' => 'Icon two',
  'Title two' => 'Title two',
  'Description two' => 'Description two',
  'Icon three' => 'Icon three',
  'Title three' => 'Title three',
  'Description three' => 'Description three',
  'Sidebar Image' => 'Sidebar Image',
  'Account info is required' => 'Account info is required',
  'Currency is required' => 'Currency is required',
  'Public key is required' => 'Public key is required',
  'API key is required' => 'API key is required',
  'Auth token is required' => 'Auth token is required',
  'Mollie key is required' => 'Mollie key is required',
  'Client id is required' => 'Client id is required',
  'Client secret is required' => 'Client secret is required',
  'Razorpay key is required' => 'Razorpay key is required',
  'Stripe key is required' => 'Stripe key is required',
  'Stripe secret is required' => 'Stripe secret is required',
  'Basic Gateway' => 'Basic Gateway',
  'Stripe' => 'Stripe',
  'Paypal' => 'Paypal',
  'Razorpay' => 'Razorpay',
  'Flutterwave' => 'Flutterwave',
  'Mollie' => 'Mollie',
  'Paystack' => 'Paystack',
  'Instamojo' => 'Instamojo',
  'Bank Payment' => 'Bank Payment',
  'Stripe Configuration' => 'Stripe Configuration',
  'Stripe Key' => 'Stripe Key',
  'Stripe Secret' => 'Stripe Secret',
  'Paypal Configuration' => 'Paypal Configuration',
  'Account Mode' => 'Account Mode',
  'Live' => 'Live',
  'Sandbox' => 'Sandbox',
  'Client Id' => 'Client Id',
  'Secret Id' => 'Secret Id',
  'Razorpay Configuration' => 'Razorpay Configuration',
  'Razorpay Key' => 'Razorpay Key',
  'Secret Key' => 'Secret Key',
  'Theme Color' => 'Theme Color',
  'Flutterwave Configuration' => 'Flutterwave Configuration',
  'Public Key' => 'Public Key',
  'Mollie Configuration' => 'Mollie Configuration',
  'Mollie Key' => 'Mollie Key',
  'Paystack Configuration' => 'Paystack Configuration',
  'Instamojo Configuration' => 'Instamojo Configuration',
  'API Key' => 'API Key',
  'Auth Token' => 'Auth Token',
  'Bank Configuration' => 'Bank Configuration',
  'Account Information' => 'Account Information',
  'Withdraw approved successful' => 'Withdraw approved successful',
  'Withdraw rejected successful' => 'Withdraw rejected successful',
  'Withdraw deleted successful' => 'Withdraw deleted successful',
  'Method is required' => 'Method is required',
  'Amount is required' => 'Amount is required',
  'Amount should be numeric' => 'Amount should be numeric',
  'Bank Information is required' => 'Bank Information is required',
  'You do not have enough balance for withdraw' => 'You do not have enough balance for withdraw',
  'Withdraw request has been send. please awaiting for admin approval' => 'Withdraw request has been send. please awaiting for admin approval',
  'Multiple withdraw created under it, so you can not delete it' => 'Multiple withdraw created under it, so you can not delete it',
  'Minimum amount is required' => 'Minimum amount is required',
  'Minimum amount should be numeric' => 'Minimum amount should be numeric',
  'Maximum amount is required' => 'Maximum amount is required',
  'Maximum amount should be numeric' => 'Maximum amount should be numeric',
  'Withdraw Charge is required' => 'Withdraw Charge is required',
  'Withdraw Charge should be numeric' => 'Withdraw Charge should be numeric',
  'Seller Name' => 'Seller Name',
  'Total Amount' => 'Total Amount',
  'Withdraw Amount' => 'Withdraw Amount',
  'Withdraw Charge' => 'Withdraw Charge',
  'Create Method' => 'Create Method',
  'Method List' => 'Method List',
  'Minimum Amount' => 'Minimum Amount',
  'Maximum Amount' => 'Maximum Amount',
  'Edit Method' => 'Edit Method',
  'Amount Range' => 'Amount Range',
  'Charge' => 'Charge',
  'Seller || New Withdraw' => 'Seller || New Withdraw',
  'New Withdraw' => 'New Withdraw',
  'Withdraw Information' => 'Withdraw Information',
  'Withdraw Limit' => 'Withdraw Limit',
  'Withdraw charge' => 'Withdraw charge',
  'Bank/Account Information' => 'Bank/Account Information',
  'Send Withdraw Request' => 'Send Withdraw Request',
  'Seller || My Withdraw' => 'Seller || My Withdraw',
  'Current Balance' => 'Current Balance',
  'Total Withdraw' => 'Total Withdraw',
  'Pending Withdraw' => 'Pending Withdraw',
  'Method Name' => 'Method Name',
  'Withdraw Details' => 'Withdraw Details',
  'Charge Amount' => 'Charge Amount',
  'Bank/Account Info' => 'Bank/Account Info',
  'Withdraw Detail' => 'Withdraw Detail',
  'Make Reject' => 'Make Reject',
  'Are you realy want to approved this withdraw?' => 'Are you realy want to approved this withdraw?',
  'Rejected Confirmation' => 'Rejected Confirmation',
  'Are you realy want to rejected this withdraw?' => 'Are you realy want to rejected this withdraw?',
  'Yes, Rejected' => 'Yes, Rejected',
  'SEO title is required' => 'SEO title is required',
  'SEO description is required' => 'SEO description is required',
  'Page' => 'Page',
  'Designation is required' => 'Designation is required',
  'Create Testimonial' => 'Create Testimonial',
  'Testimonial List' => 'Testimonial List',
  'Edit Testimonial' => 'Edit Testimonial',
  'Please login as a buyer' => 'Please login as a buyer',
  'Item added to wishlist' => 'Item added to wishlist',
  'Item already added to wishlist' => 'Item already added to wishlist',
  'Item removed to wishlist' => 'Item removed to wishlist',
  'Buyer || Wishlist' => 'Buyer || Wishlist',
  'Sorry!! Wishlist Item Not Found' => 'Sorry!! Wishlist Item Not Found',
  'Whoops... Do not have any item to your wishlist' => 'Whoops... Do not have any item to your wishlist',
  'Go to Services' => 'Go to Services',
  'Max 3 Files' => 'Max 3 Files',
  'Buyer || Wallet' => 'Buyer || Wallet',
  'Wallet' => 'Wallet',
  'Add Balance' => 'Add Balance',
  'Buyer || Add Balance' => 'Buyer || Add Balance',
  'You have to add minimum 10 USD' => 'You have to add minimum 10 USD',
  'Please provide numeric value' => 'Please provide numeric value',
  'Payment gateway is required' => 'Payment gateway is required',
  'The payment has been added to your wallet' => 'The payment has been added to your wallet',
  'Stripe Payment' => 'Stripe Payment',
  'Do not have enough balance to your wallet' => 'Do not have enough balance to your wallet',
  'Used Balance' => 'Used Balance',
  'Total Balance' => 'Total Balance',
  'Wallet Transaction' => 'Wallet Transaction',
);
 ?>